# Complete HTTP Request-Response Flow in Go with Gin Framework

This document demonstrates how to implement a complete HTTP request-response flow using the Gin framework in Go, following the existing architecture patterns in this codebase.

## Architecture Overview

The application follows a layered architecture:
1. **Handler Layer** - Handles HTTP requests and responses
2. **Service Layer** - Contains business logic
3. **Port/Adapter Layer** - Database and external API interactions

## HTTP Request-Response Flow Components

### 1. Request Parsing and Binding

The Gin framework provides several methods to bind different types of request data:

#### Query Parameters (GET requests)
```go
// Structure with form tags for query parameters
type GetMembersRequest struct {
    Page     int    `form:"page" binding:"omitempty,min=1"`
    PageSize int    `form:"page_size" binding:"omitempty,min=1,max=100"`
    Search   string `form:"search" binding:"omitempty,max=100"`
    Status   string `form:"status" binding:"omitempty,oneof=active inactive"`
}

// Binding in handler
var request GetMembersRequest
if err := ctx.ShouldBindQuery(&request); err != nil {
    l.Errorf("failed when bind query parameters: %v", err)
    ctx.Error(errs.NewBadRequestError())
    ctx.Abort()
    return
}
```

#### Path Parameters (URI parameters)
```go
// Structure with uri tags for path parameters
type GetMemberByIDRequest struct {
    ID string `uri:"id" binding:"required,uuid"`
}

// Binding in handler
var request GetMemberByIDRequest
if err := ctx.ShouldBindUri(&request); err != nil {
    l.Errorf("failed when bind URI parameters: %v", err)
    ctx.Error(errs.NewBadRequestError())
    ctx.Abort()
    return
}
```

#### JSON Body (POST/PUT requests)
```go
// Structure with json tags for JSON body
type CreateMemberRequest struct {
    Name     string `json:"name" binding:"required,min=2,max=100"`
    Email    string `json:"email" binding:"required,email"`
    Phone    string `json:"phone" binding:"omitempty,min=10,max=15"`
    Status   string `json:"status" binding:"omitempty,oneof=active inactive"`
    Metadata map[string]interface{} `json:"metadata" binding:"omitempty"`
}

// Binding in handler
var request CreateMemberRequest
if err := ctx.ShouldBindJSON(&request); err != nil {
    l.Errorf("failed when bind JSON request: %v", err)
    ctx.Error(errs.NewBadRequestError())
    ctx.Abort()
    return
}
```

#### Combined Parameters (Path + JSON)
```go
// For PUT requests that need both path parameters and JSON body
type UpdateMemberRequest struct {
    ID       string `uri:"id" binding:"required,uuid"`        // Path parameter
    Name     string `json:"name" binding:"omitempty,min=2,max=100"`  // JSON body
    Email    string `json:"email" binding:"omitempty,email"`         // JSON body
    // ... other fields
}

// Binding in handler (order matters)
var request UpdateMemberRequest
// First bind URI parameters
if err := ctx.ShouldBindUri(&request); err != nil {
    // handle error
}
// Then bind JSON body
if err := ctx.ShouldBindJSON(&request); err != nil {
    // handle error
}
```

### 2. Validation Tags

The framework supports various validation tags:
- `required` - Field is mandatory
- `omitempty` - Field is optional
- `min=N, max=N` - String length or numeric range validation
- `email` - Email format validation
- `uuid` - UUID format validation
- `oneof=val1 val2` - Value must be one of the specified options

### 3. Handler Function Structure

```go
func (h *memberHandler) CreateMember(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
    // 1. Parse and bind request
    var request member.CreateMemberRequest
    if err := ctx.ShouldBindJSON(&request); err != nil {
        l.Errorf("failed when bind JSON request: %v", err)
        ctx.Error(errs.NewBadRequestError())
        ctx.Abort()
        return
    }

    // 2. Log request details
    l.Infof("received create member request: name=%s, email=%s", request.Name, request.Email)

    // 3. Call service layer
    response, err := h.service.CreateMember(&request, rctx, l)
    if err != nil {
        l.Errorf("service error: %v", err)
        ctx.Error(err)
        ctx.Abort()
        return
    }

    // 4. Return response with appropriate status code
    ctx.JSON(http.StatusCreated, response)
}
```

### 4. Service Layer Implementation

```go
func (s *service) CreateMember(request *CreateMemberRequest, rctx *contexts.RouteContext, l logger.Logger) (*CreateMemberResponse, errs.Error) {
    // 1. Validate request using struct validation
    if err := infrastructure.Validate.Struct(request); err != nil {
        l.Errorf("failed when validate request: %v", err)
        return nil, errs.NewBadRequestError()
    }

    // 2. Apply business logic (set defaults, transformations)
    if request.Status == "" {
        request.Status = "active"
    }

    // 3. Call database layer
    dbRequest := &calldb.Request{
        // Map request to database request structure
    }
    
    _, err := s.callDb.Execute(dbRequest, rctx, l)
    if err != nil {
        l.Errorf("failed to create member in database: %v", err)
        return nil, err
    }

    // 4. Build and return response
    member := MemberInfo{
        ID:        "generated-uuid",
        Name:      request.Name,
        Email:     request.Email,
        // ... other fields
    }

    response := &CreateMemberResponse{
        Member:  member,
        Message: "Member created successfully",
    }

    l.Infof("successfully created member with email: %s", request.Email)
    return response, nil
}
```

## HTTP Methods and Status Codes

### GET Requests
- **Purpose**: Retrieve data
- **Parameters**: Query parameters, path parameters
- **Success Status**: 200 OK
- **Example**: `GET /member?page=1&page_size=10`

### POST Requests
- **Purpose**: Create new resources
- **Parameters**: JSON body
- **Success Status**: 201 Created
- **Example**: `POST /member` with JSON body

### PUT Requests
- **Purpose**: Update existing resources
- **Parameters**: Path parameters + JSON body
- **Success Status**: 200 OK
- **Example**: `PUT /member/123` with JSON body

### DELETE Requests
- **Purpose**: Delete resources
- **Parameters**: Path parameters
- **Success Status**: 200 OK
- **Example**: `DELETE /member/123`

## Error Handling

The application uses a centralized error handling approach:

1. **Custom Error Types**: All errors implement the `errs.Error` interface
2. **Error Middleware**: Automatically converts errors to appropriate HTTP responses
3. **Consistent Error Format**: All errors return structured JSON responses

```go
// In handler
if err != nil {
    ctx.Error(err)  // Add error to context
    ctx.Abort()     // Stop processing
    return
}

// Error middleware automatically handles the response
```

## Route Registration

```go
func BindMemberRoute(app gins.GinApps) {
    svc := member.New(
        calldb.NewAdaptorPG(infrastructure.Db),
        callapi.NewAdaptorAPI(callapi.NewClient()),
    )
    hdl := NewMemberHandler(svc)
    
    // Register routes for different HTTP methods
    app.Register(http.MethodGet, "/member", app.ParseRouteContext(hdl.GetMembers))
    app.Register(http.MethodGet, "/member/:id", app.ParseRouteContext(hdl.GetMemberByID))
    app.Register(http.MethodPost, "/member", app.ParseRouteContext(hdl.CreateMember))
    app.Register(http.MethodPut, "/member/:id", app.ParseRouteContext(hdl.UpdateMember))
    app.Register(http.MethodDelete, "/member/:id", app.ParseRouteContext(hdl.DeleteMember))
}
```

## Best Practices

1. **Use appropriate binding methods**: `ShouldBindQuery`, `ShouldBindUri`, `ShouldBindJSON`
2. **Validate input**: Use both binding validation and struct validation
3. **Log appropriately**: Log request details and errors
4. **Return correct status codes**: 200 OK, 201 Created, 400 Bad Request, etc.
5. **Handle errors consistently**: Use the error middleware pattern
6. **Structure responses**: Use consistent response formats
7. **Separate concerns**: Keep handlers thin, put business logic in services

## Complete Implementation Example

The member handler implementation demonstrates all these patterns:

### File Structure
```
internal/portal/
├── handler/
│   ├── member.go          # HTTP handlers for all CRUD operations
│   └── member_test.go     # Comprehensive tests
├── service/member/
│   ├── domain.go          # Request/response structures and service interface
│   └── service.go         # Business logic implementation
└── port/
    ├── call-db/           # Database adapter
    └── call-api/          # External API adapter
```

### Key Features Implemented

1. **Multiple HTTP Methods**: GET, POST, PUT, DELETE
2. **Parameter Handling**: Query parameters, path parameters, JSON body
3. **Validation**: Request validation with detailed error messages
4. **Error Handling**: Centralized error handling with appropriate HTTP status codes
5. **Logging**: Comprehensive logging throughout the request lifecycle
6. **Testing**: Unit tests with mocks and benchmarks
7. **Documentation**: Complete API documentation with examples

### Running the Implementation

1. **Start the server**:
   ```bash
   go run main.go
   ```

2. **Test the endpoints** using the examples in `docs/api-examples.md`

3. **Run tests**:
   ```bash
   go test ./internal/portal/handler/...
   ```

This implementation provides a solid foundation for building RESTful APIs in Go using the Gin framework while following clean architecture principles.
