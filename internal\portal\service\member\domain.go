package member

import (
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
)

type Service interface {
	// Legacy method - keeping for backward compatibility
	Execute(request *Request, rctx *contexts.RouteContext, l logger.Logger) (*Response, errs.Error)

	// GET /member - Get member list with query parameters
	GetMembers(request *GetMembersRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetMembersResponse, errs.Error)

	// GET /member/:id - Get member by ID
	GetMemberByID(request *GetMemberByIDRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetMemberResponse, errs.Error)

	// POST /member - Create new member
	CreateMember(request *CreateMemberRequest, rctx *contexts.RouteContext, l logger.Logger) (*CreateMemberResponse, errs.Error)

	// PUT /member/:id - Update member
	UpdateMember(request *UpdateMemberRequest, rctx *contexts.RouteContext, l logger.Logger) (*UpdateMemberResponse, errs.Error)

	// DELETE /member/:id - Delete member
	DeleteMember(request *DeleteMemberRequest, rctx *contexts.RouteContext, l logger.Logger) (*DeleteMemberResponse, errs.Error)
}

// GET /member - Query parameters for listing members
type GetMembersRequest struct {
	Page     int    `form:"page" binding:"omitempty,min=1" json:"page"`
	PageSize int    `form:"page_size" binding:"omitempty,min=1,max=100" json:"page_size"`
	Search   string `form:"search" binding:"omitempty,max=100" json:"search"`
	Status   string `form:"status" binding:"omitempty,oneof=active inactive" json:"status"`
}

type GetMembersResponse struct {
	Members    []MemberInfo `json:"members"`
	Pagination Pagination   `json:"pagination"`
}

// GET /member/:id - Path parameter for getting member by ID
type GetMemberByIDRequest struct {
	ID string `uri:"id" binding:"required,uuid" json:"id"`
}

type GetMemberResponse struct {
	Member MemberInfo `json:"member"`
}

// POST /member - JSON body for creating member
type CreateMemberRequest struct {
	Name     string                 `json:"name" binding:"required,min=2,max=100" validate:"required"`
	Email    string                 `json:"email" binding:"required,email" validate:"required,email"`
	Phone    string                 `json:"phone" binding:"omitempty,min=10,max=15" validate:"omitempty"`
	Status   string                 `json:"status" binding:"omitempty,oneof=active inactive" validate:"omitempty,oneof=active inactive"`
	Metadata map[string]interface{} `json:"metadata" binding:"omitempty"`
}

type CreateMemberResponse struct {
	Member  MemberInfo `json:"member"`
	Message string     `json:"message"`
}

// PUT /member/:id - Path parameter + JSON body for updating member
type UpdateMemberRequest struct {
	ID       string                 `uri:"id" binding:"required,uuid" json:"id"`
	Name     string                 `json:"name" binding:"omitempty,min=2,max=100" validate:"omitempty"`
	Email    string                 `json:"email" binding:"omitempty,email" validate:"omitempty,email"`
	Phone    string                 `json:"phone" binding:"omitempty,min=10,max=15" validate:"omitempty"`
	Status   string                 `json:"status" binding:"omitempty,oneof=active inactive" validate:"omitempty,oneof=active inactive"`
	Metadata map[string]interface{} `json:"metadata" binding:"omitempty"`
}

type UpdateMemberResponse struct {
	Member  MemberInfo `json:"member"`
	Message string     `json:"message"`
}

// DELETE /member/:id - Path parameter for deleting member
type DeleteMemberRequest struct {
	ID string `uri:"id" binding:"required,uuid" json:"id"`
}

type DeleteMemberResponse struct {
	Message string `json:"message"`
}

// Common structures
type MemberInfo struct {
	ID        string                 `json:"id"`
	Name      string                 `json:"name"`
	Email     string                 `json:"email"`
	Phone     string                 `json:"phone,omitempty"`
	Status    string                 `json:"status"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt string                 `json:"created_at"`
	UpdatedAt string                 `json:"updated_at"`
}

type Pagination struct {
	Page      int   `json:"page"`
	PageSize  int   `json:"page_size"`
	Total     int64 `json:"total"`
	TotalPage int   `json:"total_page"`
}

// Legacy support - keeping original structures for backward compatibility
type Request struct{}
type Response struct{}
