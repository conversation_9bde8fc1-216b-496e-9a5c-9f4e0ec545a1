# API Examples - Member Management

This document provides practical examples of how to use the Member Management API endpoints.

## Base URL
```
http://localhost:8080
```

## 1. GET /member - List Members with Query Parameters

### Request
```http
GET /member?page=1&page_size=10&search=john&status=active HTTP/1.1
Host: localhost:8080
Content-Type: application/json
```

### Query Parameters
- `page` (optional): Page number (default: 1, min: 1)
- `page_size` (optional): Items per page (default: 10, min: 1, max: 100)
- `search` (optional): Search term (max: 100 characters)
- `status` (optional): Member status ("active" or "inactive")

### Response (200 OK)
```json
{
  "members": [
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "status": "active",
      "metadata": {
        "department": "Engineering",
        "role": "Senior Developer"
      },
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total": 1,
    "total_page": 1
  }
}
```

## 2. GET /member/:id - Get Member by ID

### Request
```http
GET /member/123e4567-e89b-12d3-a456-************ HTTP/1.1
Host: localhost:8080
Content-Type: application/json
```

### Path Parameters
- `id` (required): Member UUID

### Response (200 OK)
```json
{
  "member": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "status": "active",
    "metadata": {
      "department": "Engineering",
      "role": "Senior Developer"
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

## 3. POST /member - Create New Member

### Request
```http
POST /member HTTP/1.1
Host: localhost:8080
Content-Type: application/json

{
  "name": "Jane Smith",
  "email": "<EMAIL>",
  "phone": "+1987654321",
  "status": "active",
  "metadata": {
    "department": "Marketing",
    "role": "Marketing Manager"
  }
}
```

### Request Body Fields
- `name` (required): Member name (min: 2, max: 100 characters)
- `email` (required): Valid email address
- `phone` (optional): Phone number (min: 10, max: 15 characters)
- `status` (optional): "active" or "inactive" (default: "active")
- `metadata` (optional): Additional key-value data

### Response (201 Created)
```json
{
  "member": {
    "id": "123e4567-e89b-12d3-a456-426614174001",
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "phone": "+1987654321",
    "status": "active",
    "metadata": {
      "department": "Marketing",
      "role": "Marketing Manager"
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "message": "Member created successfully"
}
```

## 4. PUT /member/:id - Update Member

### Request
```http
PUT /member/123e4567-e89b-12d3-a456-************ HTTP/1.1
Host: localhost:8080
Content-Type: application/json

{
  "name": "John Doe Updated",
  "email": "<EMAIL>",
  "phone": "+1234567899",
  "status": "inactive",
  "metadata": {
    "department": "Engineering",
    "role": "Lead Developer",
    "team": "Backend"
  }
}
```

### Path Parameters
- `id` (required): Member UUID

### Request Body Fields (all optional for updates)
- `name` (optional): Member name (min: 2, max: 100 characters)
- `email` (optional): Valid email address
- `phone` (optional): Phone number (min: 10, max: 15 characters)
- `status` (optional): "active" or "inactive"
- `metadata` (optional): Additional key-value data

### Response (200 OK)
```json
{
  "member": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "John Doe Updated",
    "email": "<EMAIL>",
    "phone": "+1234567899",
    "status": "inactive",
    "metadata": {
      "department": "Engineering",
      "role": "Lead Developer",
      "team": "Backend"
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T12:00:00Z"
  },
  "message": "Member updated successfully"
}
```

## 5. DELETE /member/:id - Delete Member

### Request
```http
DELETE /member/123e4567-e89b-12d3-a456-************ HTTP/1.1
Host: localhost:8080
```

### Path Parameters
- `id` (required): Member UUID

### Response (200 OK)
```json
{
  "message": "Member deleted successfully"
}
```

## Error Responses

### 400 Bad Request - Validation Error
```json
{
  "code": "40000",
  "message": "Bad Request",
  "description": "Invalid request parameters or body"
}
```

### 404 Not Found - Resource Not Found
```json
{
  "code": "40400",
  "message": "Not Found",
  "description": "The requested resource was not found"
}
```

### 500 Internal Server Error
```json
{
  "code": "50001",
  "message": "Internal Server Error",
  "description": "An unexpected error occurred"
}
```

## cURL Examples

### Get Members List
```bash
curl -X GET "http://localhost:8080/member?page=1&page_size=10&status=active" \
  -H "Content-Type: application/json"
```

### Get Member by ID
```bash
curl -X GET "http://localhost:8080/member/123e4567-e89b-12d3-a456-************" \
  -H "Content-Type: application/json"
```

### Create Member
```bash
curl -X POST "http://localhost:8080/member" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "phone": "+1987654321",
    "status": "active"
  }'
```

### Update Member
```bash
curl -X PUT "http://localhost:8080/member/123e4567-e89b-12d3-a456-************" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe Updated",
    "status": "inactive"
  }'
```

### Delete Member
```bash
curl -X DELETE "http://localhost:8080/member/123e4567-e89b-12d3-a456-************"
```

## Testing with Different Tools

### Postman Collection
You can import these examples into Postman by creating a new collection and adding requests with the above configurations.

### HTTPie Examples
```bash
# Get members
http GET localhost:8080/member page==1 page_size==10

# Create member
http POST localhost:8080/member name="Jane Smith" email="<EMAIL>"

# Update member
http PUT localhost:8080/member/123e4567-e89b-12d3-a456-************ name="Updated Name"
```
