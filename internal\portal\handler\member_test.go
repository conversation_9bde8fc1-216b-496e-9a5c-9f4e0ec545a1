package handler

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"digital-transformation-api/internal/portal/service/member"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Mock service for testing
type mockMemberService struct {
	mock.Mock
}

func (m *mockMemberService) Execute(request *member.Request, rctx *contexts.RouteContext, l logger.Logger) (*member.Response, errs.Error) {
	args := m.Called(request, rctx, l)
	if args.Get(1) != nil {
		return nil, args.Get(1).(errs.Error)
	}
	return args.Get(0).(*member.Response), nil
}

func (m *mockMemberService) GetMembers(request *member.GetMembersRequest, rctx *contexts.RouteContext, l logger.Logger) (*member.GetMembersResponse, errs.Error) {
	args := m.Called(request, rctx, l)
	if args.Get(1) != nil {
		return nil, args.Get(1).(errs.Error)
	}
	return args.Get(0).(*member.GetMembersResponse), nil
}

func (m *mockMemberService) GetMemberByID(request *member.GetMemberByIDRequest, rctx *contexts.RouteContext, l logger.Logger) (*member.GetMemberResponse, errs.Error) {
	args := m.Called(request, rctx, l)
	if args.Get(1) != nil {
		return nil, args.Get(1).(errs.Error)
	}
	return args.Get(0).(*member.GetMemberResponse), nil
}

func (m *mockMemberService) CreateMember(request *member.CreateMemberRequest, rctx *contexts.RouteContext, l logger.Logger) (*member.CreateMemberResponse, errs.Error) {
	args := m.Called(request, rctx, l)
	if args.Get(1) != nil {
		return nil, args.Get(1).(errs.Error)
	}
	return args.Get(0).(*member.CreateMemberResponse), nil
}

func (m *mockMemberService) UpdateMember(request *member.UpdateMemberRequest, rctx *contexts.RouteContext, l logger.Logger) (*member.UpdateMemberResponse, errs.Error) {
	args := m.Called(request, rctx, l)
	if args.Get(1) != nil {
		return nil, args.Get(1).(errs.Error)
	}
	return args.Get(0).(*member.UpdateMemberResponse), nil
}

func (m *mockMemberService) DeleteMember(request *member.DeleteMemberRequest, rctx *contexts.RouteContext, l logger.Logger) (*member.DeleteMemberResponse, errs.Error) {
	args := m.Called(request, rctx, l)
	if args.Get(1) != nil {
		return nil, args.Get(1).(errs.Error)
	}
	return args.Get(0).(*member.DeleteMemberResponse), nil
}

// Test helper functions
func setupTestRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	return router
}

func createTestContext(method, url string, body interface{}) (*gin.Context, *httptest.ResponseRecorder) {
	w := httptest.NewRecorder()
	
	var req *http.Request
	if body != nil {
		jsonBody, _ := json.Marshal(body)
		req = httptest.NewRequest(method, url, bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
	} else {
		req = httptest.NewRequest(method, url, nil)
	}
	
	router := setupTestRouter()
	ctx, _ := gin.CreateTestContext(w)
	ctx.Request = req
	
	// Set route context for testing
	rctx := &contexts.RouteContext{}
	ctx.Set("rctx", rctx)
	
	return ctx, w
}

// Test GET /member - List members with query parameters
func TestGetMembers_Success(t *testing.T) {
	// Arrange
	mockService := new(mockMemberService)
	handler := NewMemberHandler(mockService)
	
	expectedResponse := &member.GetMembersResponse{
		Members: []member.MemberInfo{
			{
				ID:    "123e4567-e89b-12d3-a456-426614174000",
				Name:  "John Doe",
				Email: "<EMAIL>",
				Status: "active",
			},
		},
		Pagination: member.Pagination{
			Page:      1,
			PageSize:  10,
			Total:     1,
			TotalPage: 1,
		},
	}
	
	mockService.On("GetMembers", mock.AnythingOfType("*member.GetMembersRequest"), mock.Anything, mock.Anything).Return(expectedResponse, nil)
	
	ctx, w := createTestContext("GET", "/member?page=1&page_size=10&status=active", nil)
	rctx := &contexts.RouteContext{}
	
	// Act
	handler.GetMembers(ctx, rctx, &mockLogger{})
	
	// Assert
	assert.Equal(t, http.StatusOK, w.Code)
	
	var response member.GetMembersResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, expectedResponse.Members[0].ID, response.Members[0].ID)
	assert.Equal(t, expectedResponse.Pagination.Total, response.Pagination.Total)
	
	mockService.AssertExpectations(t)
}

// Test POST /member - Create member with JSON body
func TestCreateMember_Success(t *testing.T) {
	// Arrange
	mockService := new(mockMemberService)
	handler := NewMemberHandler(mockService)
	
	requestBody := member.CreateMemberRequest{
		Name:   "Jane Smith",
		Email:  "<EMAIL>",
		Phone:  "+1987654321",
		Status: "active",
	}
	
	expectedResponse := &member.CreateMemberResponse{
		Member: member.MemberInfo{
			ID:     "123e4567-e89b-12d3-a456-426614174001",
			Name:   requestBody.Name,
			Email:  requestBody.Email,
			Phone:  requestBody.Phone,
			Status: requestBody.Status,
		},
		Message: "Member created successfully",
	}
	
	mockService.On("CreateMember", mock.AnythingOfType("*member.CreateMemberRequest"), mock.Anything, mock.Anything).Return(expectedResponse, nil)
	
	ctx, w := createTestContext("POST", "/member", requestBody)
	rctx := &contexts.RouteContext{}
	
	// Act
	handler.CreateMember(ctx, rctx, &mockLogger{})
	
	// Assert
	assert.Equal(t, http.StatusCreated, w.Code)
	
	var response member.CreateMemberResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, expectedResponse.Member.Name, response.Member.Name)
	assert.Equal(t, expectedResponse.Message, response.Message)
	
	mockService.AssertExpectations(t)
}

// Test validation error handling
func TestCreateMember_ValidationError(t *testing.T) {
	// Arrange
	mockService := new(mockMemberService)
	handler := NewMemberHandler(mockService)
	
	// Invalid request body (missing required fields)
	requestBody := member.CreateMemberRequest{
		Name: "", // Empty name should fail validation
	}
	
	ctx, w := createTestContext("POST", "/member", requestBody)
	rctx := &contexts.RouteContext{}
	
	// Act
	handler.CreateMember(ctx, rctx, &mockLogger{})
	
	// Assert
	assert.Equal(t, http.StatusBadRequest, w.Code)
	
	// Service should not be called due to validation error
	mockService.AssertNotCalled(t, "CreateMember")
}

// Test service error handling
func TestGetMemberByID_ServiceError(t *testing.T) {
	// Arrange
	mockService := new(mockMemberService)
	handler := NewMemberHandler(mockService)
	
	mockService.On("GetMemberByID", mock.AnythingOfType("*member.GetMemberByIDRequest"), mock.Anything, mock.Anything).Return(nil, errs.NewInternalError())
	
	ctx, w := createTestContext("GET", "/member/123e4567-e89b-12d3-a456-426614174000", nil)
	ctx.Params = gin.Params{{Key: "id", Value: "123e4567-e89b-12d3-a456-426614174000"}}
	rctx := &contexts.RouteContext{}
	
	// Act
	handler.GetMemberByID(ctx, rctx, &mockLogger{})
	
	// Assert
	assert.Equal(t, http.StatusInternalServerError, w.Code)
	
	mockService.AssertExpectations(t)
}

// Mock logger for testing
type mockLogger struct{}

func (m *mockLogger) Info(obj any)                        {}
func (m *mockLogger) Infof(format string, obj ...any)     {}
func (m *mockLogger) Debug(obj any)                       {}
func (m *mockLogger) Debugf(format string, obj ...any)    {}
func (m *mockLogger) Error(err any)                       {}
func (m *mockLogger) Errorf(format string, obj ...any)    {}
func (m *mockLogger) JSON(obj map[string]any)             {}

// Benchmark test for performance
func BenchmarkGetMembers(b *testing.B) {
	mockService := new(mockMemberService)
	handler := NewMemberHandler(mockService)
	
	expectedResponse := &member.GetMembersResponse{
		Members: []member.MemberInfo{
			{ID: "test", Name: "Test User", Email: "<EMAIL>"},
		},
		Pagination: member.Pagination{Page: 1, PageSize: 10, Total: 1, TotalPage: 1},
	}
	
	mockService.On("GetMembers", mock.Anything, mock.Anything, mock.Anything).Return(expectedResponse, nil)
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ctx, _ := createTestContext("GET", "/member", nil)
		rctx := &contexts.RouteContext{}
		handler.GetMembers(ctx, rctx, &mockLogger{})
	}
}
