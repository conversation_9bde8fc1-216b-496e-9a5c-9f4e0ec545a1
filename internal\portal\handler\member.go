package handler

import (
	"digital-transformation-api/infrastructure"
	callapi "digital-transformation-api/internal/portal/port/call-api"
	calldb "digital-transformation-api/internal/portal/port/call-db"
	"digital-transformation-api/internal/portal/service/member"

	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/gins"
	"digital-transformation-api/libs/logger"
	"net/http"

	"github.com/gin-gonic/gin"
)

type memberHandler struct {
	service member.Service
}

func NewMemberHandler(service member.Service) *memberHandler {
	return &memberHandler{
		service: service,
	}
}

// Legacy handler - keeping for backward compatibility
func (h *memberHandler) Handle(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	var request member.Request
	if err := ctx.BindJSON(&request); err != nil {
		l.Errorf("failed when bind request: %v", err)
		ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	response, err := h.service.Execute(&request, rctx, l)
	if err != nil {
		ctx.Error(err)
		ctx.Abort()
		return
	}

	ctx.JSON(http.StatusOK, response)
}

// GET /member - Handle getting member list with query parameters
func (h *memberHandler) GetMembers(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	var request member.GetMembersRequest

	// Bind query parameters using ShouldBindQuery
	if err := ctx.ShouldBindQuery(&request); err != nil {
		l.Errorf("failed when bind query parameters: %v", err)
		ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	l.Infof("received get members request: page=%d, pageSize=%d, search=%s, status=%s",
		request.Page, request.PageSize, request.Search, request.Status)

	response, err := h.service.GetMembers(&request, rctx, l)
	if err != nil {
		l.Errorf("service error: %v", err)
		ctx.Error(err)
		ctx.Abort()
		return
	}

	ctx.JSON(http.StatusOK, response)
}

// GET /member/:id - Handle getting member by ID with path parameter
func (h *memberHandler) GetMemberByID(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	var request member.GetMemberByIDRequest

	// Bind URI parameters using ShouldBindUri
	if err := ctx.ShouldBindUri(&request); err != nil {
		l.Errorf("failed when bind URI parameters: %v", err)
		ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	l.Infof("received get member by ID request: id=%s", request.ID)

	response, err := h.service.GetMemberByID(&request, rctx, l)
	if err != nil {
		l.Errorf("service error: %v", err)
		ctx.Error(err)
		ctx.Abort()
		return
	}

	ctx.JSON(http.StatusOK, response)
}

// POST /member - Handle creating new member with JSON body
func (h *memberHandler) CreateMember(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	var request member.CreateMemberRequest

	// Bind JSON body using ShouldBindJSON
	if err := ctx.ShouldBindJSON(&request); err != nil {
		l.Errorf("failed when bind JSON request: %v", err)
		ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	l.Infof("received create member request: name=%s, email=%s", request.Name, request.Email)

	response, err := h.service.CreateMember(&request, rctx, l)
	if err != nil {
		l.Errorf("service error: %v", err)
		ctx.Error(err)
		ctx.Abort()
		return
	}

	// Return 201 Created for successful resource creation
	ctx.JSON(http.StatusCreated, response)
}

// PUT /member/:id - Handle updating member with path parameter and JSON body
func (h *memberHandler) UpdateMember(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	var request member.UpdateMemberRequest

	// First bind URI parameters
	if err := ctx.ShouldBindUri(&request); err != nil {
		l.Errorf("failed when bind URI parameters: %v", err)
		ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	// Then bind JSON body - this will merge with the URI parameters
	if err := ctx.ShouldBindJSON(&request); err != nil {
		l.Errorf("failed when bind JSON request: %v", err)
		ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	l.Infof("received update member request: id=%s, name=%s, email=%s",
		request.ID, request.Name, request.Email)

	response, err := h.service.UpdateMember(&request, rctx, l)
	if err != nil {
		l.Errorf("service error: %v", err)
		ctx.Error(err)
		ctx.Abort()
		return
	}

	ctx.JSON(http.StatusOK, response)
}

// DELETE /member/:id - Handle deleting member with path parameter
func (h *memberHandler) DeleteMember(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	var request member.DeleteMemberRequest

	// Bind URI parameters using ShouldBindUri
	if err := ctx.ShouldBindUri(&request); err != nil {
		l.Errorf("failed when bind URI parameters: %v", err)
		ctx.Error(errs.NewBadRequestError())
		ctx.Abort()
		return
	}

	l.Infof("received delete member request: id=%s", request.ID)

	response, err := h.service.DeleteMember(&request, rctx, l)
	if err != nil {
		l.Errorf("service error: %v", err)
		ctx.Error(err)
		ctx.Abort()
		return
	}

	// Return 200 OK with success message for DELETE operations
	ctx.JSON(http.StatusOK, response)
}

func BindMemberRoute(app gins.GinApps) {
	svc := member.New(
		calldb.NewAdaptorPG(infrastructure.Db),
		callapi.NewAdaptorAPI(callapi.NewClient()),
	)

	hdl := NewMemberHandler(svc)

	// Legacy route - keeping for backward compatibility
	app.Register(
		http.MethodGet,
		"/member/legacy",
		app.ParseRouteContext(hdl.Handle),
	)

	// Modern RESTful routes demonstrating complete HTTP request-response flow

	// GET /member - Get member list with query parameters
	// Example: GET /member?page=1&page_size=10&search=john&status=active
	app.Register(
		http.MethodGet,
		"/member",
		app.ParseRouteContext(hdl.GetMembers),
	)

	// GET /member/:id - Get member by ID with path parameter
	// Example: GET /member/123e4567-e89b-12d3-a456-426614174000
	app.Register(
		http.MethodGet,
		"/member/:id",
		app.ParseRouteContext(hdl.GetMemberByID),
	)

	// POST /member - Create new member with JSON body
	// Example: POST /member with JSON body
	app.Register(
		http.MethodPost,
		"/member",
		app.ParseRouteContext(hdl.CreateMember),
	)

	// PUT /member/:id - Update member with path parameter and JSON body
	// Example: PUT /member/123e4567-e89b-12d3-a456-426614174000 with JSON body
	app.Register(
		http.MethodPut,
		"/member/:id",
		app.ParseRouteContext(hdl.UpdateMember),
	)

	// DELETE /member/:id - Delete member with path parameter
	// Example: DELETE /member/123e4567-e89b-12d3-a456-426614174000
	app.Register(
		http.MethodDelete,
		"/member/:id",
		app.ParseRouteContext(hdl.DeleteMember),
	)
}
