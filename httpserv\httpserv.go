package httpserv

import (
	"digital-transformation-api/infrastructure"
	example "digital-transformation-api/internal/example-domain/handler"
	portal "digital-transformation-api/internal/portal/handler"
	"digital-transformation-api/libs/gins"
	"digital-transformation-api/libs/gins/middleware"
	"fmt"
)

func Run() {
	app := gins.New()
	app.UseMiddleware(middleware.CORS())
	app.UseMiddleware(middleware.Log())
	app.UseMiddleware(middleware.Error())

	example.BindPaymentRoute(app)
	portal.BindMemberRoute(app)

	app.ListenAndServe(fmt.Sprintf(":%s", infrastructure.AppConfig.Port), closeFunc)
}

func closeFunc() {

}
