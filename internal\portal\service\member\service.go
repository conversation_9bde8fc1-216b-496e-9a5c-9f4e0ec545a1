package member

import (
	"digital-transformation-api/infrastructure"
	callapi "digital-transformation-api/internal/portal/port/call-api"
	calldb "digital-transformation-api/internal/portal/port/call-db"

	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
)

type service struct {
	callDb  calldb.Port
	callApi callapi.Port
}

func New(
	callDb calldb.Port,
	callApi callapi.Port,
) Service {
	return &service{
		callDb:  callDb,
		callApi: callApi,
	}
}

// Legacy method - keeping for backward compatibility
func (s *service) Execute(request *Request, rctx *contexts.RouteContext, l logger.Logger) (*Response, errs.Error) {
	if err := infrastructure.Validate.Struct(request); err != nil {
		l.Errorf("failed when validate request")
		return nil, errs.NewBadRequestError()
	}

	return nil, nil
}

// GET /member - Get member list with query parameters
func (s *service) GetMembers(request *GetMembersRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetMembersResponse, errs.Error) {
	// Validate request
	if err := infrastructure.Validate.Struct(request); err != nil {
		l.Errorf("failed when validate request: %v", err)
		return nil, errs.NewBadRequestError()
	}

	// Set default values
	if request.Page <= 0 {
		request.Page = 1
	}
	if request.PageSize <= 0 {
		request.PageSize = 10
	}

	// Call database layer to get members
	dbRequest := &calldb.Request{
		// Map request to database request structure
		// This would contain the actual query parameters
	}

	_, err := s.callDb.Execute(dbRequest, rctx, l)
	if err != nil {
		l.Errorf("failed to get members from database: %v", err)
		return nil, err
	}

	// Mock response for demonstration
	members := []MemberInfo{
		{
			ID:        "123e4567-e89b-12d3-a456-426614174000",
			Name:      "John Doe",
			Email:     "<EMAIL>",
			Phone:     "+1234567890",
			Status:    "active",
			CreatedAt: "2024-01-01T00:00:00Z",
			UpdatedAt: "2024-01-01T00:00:00Z",
		},
	}

	totalRecords := int64(1)
	totalPages := int((totalRecords + int64(request.PageSize) - 1) / int64(request.PageSize))

	response := &GetMembersResponse{
		Members: members,
		Pagination: Pagination{
			Page:      request.Page,
			PageSize:  request.PageSize,
			Total:     totalRecords,
			TotalPage: totalPages,
		},
	}

	l.Infof("successfully retrieved %d members", len(members))
	return response, nil
}

// GET /member/:id - Get member by ID
func (s *service) GetMemberByID(request *GetMemberByIDRequest, rctx *contexts.RouteContext, l logger.Logger) (*GetMemberResponse, errs.Error) {
	// Validate request
	if err := infrastructure.Validate.Struct(request); err != nil {
		l.Errorf("failed when validate request: %v", err)
		return nil, errs.NewBadRequestError()
	}

	// Call database layer to get member by ID
	dbRequest := &calldb.Request{
		// Map request to database request structure
	}

	_, err := s.callDb.Execute(dbRequest, rctx, l)
	if err != nil {
		l.Errorf("failed to get member by ID from database: %v", err)
		return nil, err
	}

	// Mock response for demonstration
	member := MemberInfo{
		ID:        request.ID,
		Name:      "John Doe",
		Email:     "<EMAIL>",
		Phone:     "+1234567890",
		Status:    "active",
		CreatedAt: "2024-01-01T00:00:00Z",
		UpdatedAt: "2024-01-01T00:00:00Z",
	}

	response := &GetMemberResponse{
		Member: member,
	}

	l.Infof("successfully retrieved member with ID: %s", request.ID)
	return response, nil
}

// POST /member - Create new member
func (s *service) CreateMember(request *CreateMemberRequest, rctx *contexts.RouteContext, l logger.Logger) (*CreateMemberResponse, errs.Error) {
	// Validate request
	if err := infrastructure.Validate.Struct(request); err != nil {
		l.Errorf("failed when validate request: %v", err)
		return nil, errs.NewBadRequestError()
	}

	// Set default status if not provided
	if request.Status == "" {
		request.Status = "active"
	}

	// Call database layer to create member
	dbRequest := &calldb.Request{
		// Map request to database request structure
	}

	_, err := s.callDb.Execute(dbRequest, rctx, l)
	if err != nil {
		l.Errorf("failed to create member in database: %v", err)
		return nil, err
	}

	// Mock response for demonstration
	member := MemberInfo{
		ID:        "123e4567-e89b-12d3-a456-************", // Generated UUID
		Name:      request.Name,
		Email:     request.Email,
		Phone:     request.Phone,
		Status:    request.Status,
		Metadata:  request.Metadata,
		CreatedAt: "2024-01-01T00:00:00Z",
		UpdatedAt: "2024-01-01T00:00:00Z",
	}

	response := &CreateMemberResponse{
		Member:  member,
		Message: "Member created successfully",
	}

	l.Infof("successfully created member with email: %s", request.Email)
	return response, nil
}

// PUT /member/:id - Update member
func (s *service) UpdateMember(request *UpdateMemberRequest, rctx *contexts.RouteContext, l logger.Logger) (*UpdateMemberResponse, errs.Error) {
	// Validate request
	if err := infrastructure.Validate.Struct(request); err != nil {
		l.Errorf("failed when validate request: %v", err)
		return nil, errs.NewBadRequestError()
	}

	// Call database layer to update member
	dbRequest := &calldb.Request{
		// Map request to database request structure
	}

	_, err := s.callDb.Execute(dbRequest, rctx, l)
	if err != nil {
		l.Errorf("failed to update member in database: %v", err)
		return nil, err
	}

	// Mock response for demonstration
	member := MemberInfo{
		ID:        request.ID,
		Name:      request.Name,
		Email:     request.Email,
		Phone:     request.Phone,
		Status:    request.Status,
		Metadata:  request.Metadata,
		CreatedAt: "2024-01-01T00:00:00Z",
		UpdatedAt: "2024-01-01T12:00:00Z", // Updated timestamp
	}

	response := &UpdateMemberResponse{
		Member:  member,
		Message: "Member updated successfully",
	}

	l.Infof("successfully updated member with ID: %s", request.ID)
	return response, nil
}

// DELETE /member/:id - Delete member
func (s *service) DeleteMember(request *DeleteMemberRequest, rctx *contexts.RouteContext, l logger.Logger) (*DeleteMemberResponse, errs.Error) {
	// Validate request
	if err := infrastructure.Validate.Struct(request); err != nil {
		l.Errorf("failed when validate request: %v", err)
		return nil, errs.NewBadRequestError()
	}

	// Call database layer to delete member
	dbRequest := &calldb.Request{
		// Map request to database request structure
	}

	_, err := s.callDb.Execute(dbRequest, rctx, l)
	if err != nil {
		l.Errorf("failed to delete member from database: %v", err)
		return nil, err
	}

	response := &DeleteMemberResponse{
		Message: "Member deleted successfully",
	}

	l.Infof("successfully deleted member with ID: %s", request.ID)
	return response, nil
}
